# 小红书自动爬虫

基于Playwright实现的小红书内容爬取工具，支持搜索、内容提取、图片下载等功能。

## 功能特性

- ✅ **自动登录检测**: 智能检测登录状态，支持手动登录等待
- ✅ **关键词搜索**: 支持自定义搜索关键词
- ✅ **内容爬取**: 提取帖子标题、内容、作者、发布时间、点赞数等信息
- ✅ **图片下载**: 自动下载帖子中的图片到本地，按帖子标题分文件夹存储
- ✅ **数据过滤**: 智能过滤页面框架内容，只保留有效信息
- ✅ **反爬虫机制**: 随机延时、模拟真实用户行为
- ✅ **智能去重**: 自动记录已爬取帖子，避免重复爬取
- ✅ **智能滚动**: 当搜索结果不足时自动滚动加载更多帖子
- ✅ **数据库存储**: 支持SQLite数据库存储，自动记录搜索关键词
- ✅ **数据迁移**: 支持将现有JSON数据迁移到数据库
- ✅ **配置化**: 支持配置文件自定义各种参数
- ✅ **错误处理**: 完善的错误处理和重试机制
- ✅ **日志记录**: 详细的操作日志记录

## 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装Playwright浏览器
playwright install chromium
```

## 使用方法

### 基本使用

```bash
# 使用默认配置运行（搜索"笑话"，爬取2个帖子）
python xiaohongshu_crawler.py

# 指定搜索关键词和数量
python xiaohongshu_crawler.py --keyword "美食" --count 5

# 无头模式运行
python xiaohongshu_crawler.py --headless

# 调试模式
python xiaohongshu_crawler.py --debug

# 清空已爬取记录
python xiaohongshu_crawler.py --clear-records

# 禁用去重功能
python xiaohongshu_crawler.py --disable-dedup
```

### 数据库功能

项目现在支持SQLite数据库存储，自动记录搜索关键词：

```bash
# 数据迁移：将现有JSON数据导入数据库
python migrate_json_to_db.py --current-dir --keyword "笑话"

# 迁移指定文件
python migrate_json_to_db.py --input xiaohongshu_crawl_results_20250802_172004.json --keyword "美食"

# 迁移指定目录
python migrate_json_to_db.py --input ./data --keyword "旅游"

# 测试数据库功能
python test_database_functionality.py
```

### 命令行参数

- `--keyword, -k`: 搜索关键词
- `--count, -c`: 爬取帖子数量
- `--config`: 配置文件路径（默认：config.json）
- `--headless`: 无头模式运行
- `--debug, -d`: 调试模式，输出更多信息
- `--clear-records`: 清空已爬取记录后退出
- `--disable-dedup`: 禁用去重功能

### 配置文件

编辑 `config.json` 文件可以自定义各种参数：

```json
{
  "crawler_settings": {
    "default_keyword": "笑话",
    "default_post_count": 2,
    "headless_mode": false,
    "timeout_ms": 30000,
    "login_wait_time": 300,
    "delay_between_posts": 2,
    "delay_after_click": 3,
    "page_load_delay": 3,
    "login_check_interval": 5
  },
  "selectors": {
    "search_input": "#search-input",
    "note_items": ".note-item",
    // ... 更多选择器配置
  },
  "output_settings": {
    "output_file": "xiaohongshu_crawl_results.json",
    "images_folder": "images",
    "include_timestamp": true
  },
  "image_download": {
    "enabled": true,
    "max_images_per_post": 10,
    "timeout_seconds": 30
  },
  "database": {
    "enabled": true,
    "db_path": "xiaohongshu_crawl.db",
    "use_database": true,
    "fallback_to_json": true
  },
  "deduplication": {
    "enabled": true,
    "storage_file": "crawled_posts.json",
    "storage_format": "json"
  }
}
```

## 数据存储

### 数据库存储

爬取结果自动保存到SQLite数据库中，包含以下字段：

- `post_id`: 帖子ID（唯一标识）
- `search_keyword`: 搜索关键词（新增字段）
- `url`: 帖子URL
- `title`: 帖子标题
- `content`: 帖子内容
- `author`: 作者名称
- `publish_time`: 发布时间
- `like_count`: 点赞数
- `comment_count`: 评论数
- `tags`: 标签列表（JSON格式）
- `images`: 图片URL列表（JSON格式）
- `downloaded_images`: 本地图片路径列表（JSON格式）
- `crawl_time`: 爬取时间
- `created_at`: 记录创建时间

### 数据查询

可以使用数据库管理器查询数据：

```python
from database_manager import DatabaseManager

# 创建数据库管理器
db = DatabaseManager("xiaohongshu_crawl.db")

# 获取帖子总数
total = db.get_post_count()
print(f"总帖子数: {total}")

# 按关键词查询
posts = db.get_posts_by_keyword("美食")
print(f"美食相关帖子: {len(posts)} 个")

# 获取关键词统计
stats = db.get_keywords_stats()
for stat in stats:
    print(f"{stat['keyword']}: {stat['count']} 个帖子")

# 关闭连接
db.close()
```

## 图片下载功能

程序会自动下载帖子中的**真实内容图片**（而非封面图或缩略图），并按以下规则组织：

### 智能图片识别
1. **容器优先策略**:
   - 首选：从 `.note-slider` 容器中提取真实内容图片
   - 去重：自动去除轮播图中的重复图片
   - 备选：从其他图片容器中筛选
2. **封面图片智能识别**:
   - 🎯 装饰图识别：跳过 `!nd_dft_wgth_webp_3` 后缀的装饰图片
   - 🎯 真实封面：识别 `!nd_dft_wlteh_webp_3` 后缀的真正封面图
   - 🎯 智能选择：优先选择明确标记的封面图片
   - 🎯 兜底策略：如无明确封面，选择第一张非装饰图为封面
   - 🎯 最终保障：如全是装饰图，使用第一张作为封面
3. **质量过滤**:
   - ✅ 高质量内容图片（尺寸 > 300x300）
   - ✅ 来自CDN的图片（`xhscdn.com`）
   - ❌ 头像图片（包含`avatar`）
   - ❌ 小尺寸图标和装饰元素

### 存储组织
1. **文件夹结构**: `images/帖子标题_帖子ID前8位/`
2. **智能文件命名**:
   - 真正封面图：`cover.jpg` (智能识别的实际封面)
   - 内容图片：`image_01.jpg`, `image_02.webp` 等 (按正确顺序编号)
   - 装饰图片：`decoration_01.jpg` (装饰性图片不会被命名为封面)
3. **支持格式**: JPG, PNG, WebP (保持原始格式)
4. **自动清理**: 文件夹名会自动移除非法字符
5. **防重名**: 添加帖子ID前缀避免重名
6. **去重机制**: 自动跳过重复的图片URL

示例文件结构：
```
images/
├── 上班别看，容易笑喷，直呼有病_65f53bd2/
│   ├── cover.webp              # 智能识别的真正封面
│   ├── image_01.webp           # 内容图片
│   ├── image_02.jpg            # 内容图片
│   └── decoration_01.webp      # 装饰图片（如果有）
└── 一听就会哈哈哈哈哈哈哈哈的压箱底笑话_6690d8de/
    ├── cover.jpg               # 智能识别的真正封面
    └── image_01.webp           # 内容图片
```

## 智能滚动加载功能

当搜索结果页面的帖子数量不足以满足爬取需求时，程序会自动滚动页面加载更多帖子：

### 功能特性

1. **自动检测**: 检查当前页面帖子数量是否满足需求
2. **智能滚动**: 自动滚动到页面底部触发更多内容加载
3. **加载等待**: 每次滚动后等待新内容加载完成
4. **防无限循环**: 设置最大滚动次数，避免无限滚动
5. **实时反馈**: 在日志中显示滚动进度和加载结果

### 工作流程

```
检查帖子数量 → 数量不足? → 滚动页面 → 等待加载 → 检查新数量 → 重复或完成
     ↓              ↓           ↓         ↓          ↓
   足够数量      继续滚动    触发加载   新内容出现   达到目标
     ↓              ↓           ↓         ↓          ↓
   开始爬取      最大次数    页面响应   更新计数   停止滚动
```

### 配置参数

- **目标数量**: 根据用户指定的 `--count` 参数确定
- **最大滚动次数**: 默认5次，避免无限滚动
- **加载延时**: 每次滚动后等待3秒让内容加载
- **检测间隔**: 实时检测新帖子数量变化

### 使用场景

- **小众关键词**: 搜索结果较少时自动加载更多
- **大批量爬取**: 需要爬取大量帖子时确保有足够选择
- **提高成功率**: 减少因帖子不足导致的爬取失败

## 智能去重功能

程序会自动记录已爬取的帖子，避免重复爬取，提高效率：

### 功能特性

1. **自动记录**: 每次成功爬取帖子后，自动保存帖子ID到 `crawled_posts.json`
2. **智能过滤**: 在搜索结果页面自动过滤掉已爬取的帖子
3. **持久化存储**: 即使程序异常退出，已记录的帖子ID也不会丢失
4. **详细记录**: 保存帖子ID、爬取时间、标题、作者等信息

### 存储格式

```json
{
  "67ae0cb2000000002803e09d": {
    "crawl_time": "2025-08-02T17:00:00",
    "title": "上班别看，容易笑喷，直呼有病",
    "author": "风言风语文案"
  },
  "65f53bd2000000001203ecc9": {
    "crawl_time": "2025-08-02T17:05:00",
    "title": "一听就会哈哈哈哈哈哈哈哈的压箱底笑话",
    "author": "吱吱吱"
  }
}
```

### 管理命令

```bash
# 查看已爬取记录数量（在日志中显示）
python xiaohongshu_crawler.py --debug

# 清空所有已爬取记录
python xiaohongshu_crawler.py --clear-records

# 临时禁用去重功能
python xiaohongshu_crawler.py --disable-dedup
```

### 使用场景

- **多次运行**: 可以多次运行爬虫而不会重复爬取相同内容
- **增量爬取**: 定期运行爬虫，只爬取新发布的内容
- **关键词切换**: 切换不同关键词时，已爬取的帖子会被自动跳过

## 测试功能

### 基础功能测试
运行测试脚本验证环境配置：

```bash
python test_crawler.py
```

测试内容包括：
- 配置文件加载
- 依赖包检查
- 图片文件夹创建
- 去重功能
- 爬虫初始化

### 图片提取功能测试
验证图片识别和过滤逻辑：

```bash
python test_image_extraction.py
```

测试内容包括：
- 头像图片过滤
- 缩略图过滤
- 高质量内容图片识别
- 图片数量控制

## 注意事项

1. **登录要求**: 首次运行需要手动登录小红书账号
2. **反爬虫**: 程序已内置反爬虫机制，但仍需适度使用
3. **网络环境**: 确保网络连接稳定
4. **浏览器**: 需要安装Chromium浏览器（通过playwright install安装）
5. **存储空间**: 图片下载会占用本地存储空间，请确保有足够空间

## 文件结构

```
xhs-craw/
├── xiaohongshu_crawler.py      # 主爬虫脚本
├── database_manager.py         # 数据库管理模块
├── migrate_json_to_db.py       # 数据迁移脚本
├── test_database_functionality.py  # 数据库功能测试脚本
├── test_image_extraction.py   # 图片提取功能测试脚本
├── config.json                 # 配置文件
├── requirements.txt            # Python依赖
├── README.md                   # 使用说明
├── xiaohongshu_crawl.db        # SQLite数据库文件
├── crawled_posts.json          # 已爬取帖子记录（去重，兼容模式）
├── images/                     # 图片下载目录
│   ├── 帖子标题1_postid/       # 按帖子标题分文件夹
│   │   ├── cover.jpg
│   │   └── image_01.webp
│   └── 帖子标题2_postid/
├── xiaohongshu_crawler.log     # 运行日志
├── migration.log               # 数据迁移日志
└── xiaohongshu_crawl_results_*.json  # 爬取结果（JSON备份）
```

## 开发说明

本项目分为两个阶段开发：

1. **阶段一**: 手动演示和记录过程（详见 `stage1_operation_record.md`）
2. **阶段二**: 编写自动化Python脚本（当前版本）

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和网站使用条款。