#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的数据
"""

import sys
from database_manager import DatabaseManager

def check_database():
    """检查数据库中的数据"""
    print("=== 检查数据库数据 ===\n")
    
    try:
        # 初始化数据库管理器
        db_manager = DatabaseManager("xiaohongshu_crawl.db")
        
        # 获取所有帖子
        all_posts = db_manager.get_all_posts()
        
        print(f"数据库中共有 {len(all_posts)} 个帖子")
        
        if all_posts:
            print("\n最近的帖子:")
            for i, post in enumerate(all_posts[:5]):  # 显示最近的5个帖子
                print(f"{i+1}. 帖子ID: {post['post_id']}")
                print(f"   标题: {post['title']}")
                print(f"   作者: {post['author']}")
                print(f"   搜索关键词: {post['search_keyword']}")
                print(f"   爬取时间: {post['crawl_time']}")
                print(f"   图片数量: {len(post['images'])}")
                print(f"   下载图片数量: {len(post['downloaded_images'])}")
                print()
        
        # 按关键词统计
        keywords_stats = db_manager.get_keywords_stats()
        if keywords_stats:
            print("按关键词统计:")
            for stat in keywords_stats:
                print(f"  {stat['keyword']}: {stat['count']} 个帖子")
        
        db_manager.close()
        return True
        
    except Exception as e:
        print(f"检查数据库失败: {e}")
        return False

def main():
    """主函数"""
    success = check_database()
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
