#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书爬虫数据库管理模块
负责SQLite数据库的创建、数据插入、查询等操作
"""

import sqlite3
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import os

logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理类，处理SQLite数据库操作"""
    
    def __init__(self, db_path: str = "xiaohongshu_crawl.db"):
        """初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.connection: Optional[sqlite3.Connection] = None
        self.init_database()
    
    def init_database(self):
        """初始化数据库和表结构"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # 使查询结果可以按列名访问
            
            # 创建posts表
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS posts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                post_id TEXT UNIQUE NOT NULL,
                search_keyword TEXT NOT NULL,
                url TEXT,
                title TEXT,
                content TEXT,
                author TEXT,
                publish_time TEXT,
                like_count TEXT,
                comment_count TEXT,
                images TEXT,
                tags TEXT,
                downloaded_images TEXT,
                crawl_time TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            """
            
            self.connection.execute(create_table_sql)
            
            # 创建索引
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_post_id ON posts(post_id)",
                "CREATE INDEX IF NOT EXISTS idx_search_keyword ON posts(search_keyword)",
                "CREATE INDEX IF NOT EXISTS idx_crawl_time ON posts(crawl_time)",
                "CREATE INDEX IF NOT EXISTS idx_created_at ON posts(created_at)"
            ]
            
            for index_sql in indexes:
                self.connection.execute(index_sql)
            
            self.connection.commit()
            logger.info(f"数据库初始化成功: {self.db_path}")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def insert_post(self, post_data: Dict[str, Any]) -> bool:
        """插入帖子数据到数据库
        
        Args:
            post_data: 帖子数据字典
            
        Returns:
            是否插入成功
        """
        try:
            if not self.connection:
                raise Exception("数据库连接未初始化")
            
            # 检查必需字段
            if not post_data.get('postId'):
                logger.warning("帖子数据缺少postId，跳过插入")
                return False

            if not post_data.get('search_keyword'):
                logger.warning("帖子数据缺少search_keyword，跳过插入")
                return False

            # 检查帖子是否已存在
            if self.is_post_exists(post_data.get('postId')):
                logger.debug(f"帖子已存在，跳过插入: {post_data.get('postId')}")
                return False

            # 准备插入数据
            insert_sql = """
            INSERT INTO posts (
                post_id, search_keyword, url, title, content, author,
                publish_time, like_count, comment_count, images, tags,
                downloaded_images, crawl_time
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            # 将列表字段转换为JSON字符串
            images_json = json.dumps(post_data.get('images', []), ensure_ascii=False)
            tags_json = json.dumps(post_data.get('tags', []), ensure_ascii=False)
            downloaded_images_json = json.dumps(post_data.get('downloaded_images', []), ensure_ascii=False)
            
            values = (
                post_data.get('postId'),
                post_data.get('search_keyword'),
                post_data.get('url'),
                post_data.get('title'),
                post_data.get('content'),
                post_data.get('author'),
                post_data.get('publishTime'),
                post_data.get('likeCount'),
                post_data.get('commentCount'),
                images_json,
                tags_json,
                downloaded_images_json,
                post_data.get('crawl_time')
            )
            
            self.connection.execute(insert_sql, values)
            self.connection.commit()
            
            logger.info(f"成功插入帖子数据: {post_data.get('postId')}")
            return True
            
        except sqlite3.IntegrityError as e:
            logger.warning(f"帖子已存在，跳过插入: {post_data.get('postId')} - {e}")
            return False
        except Exception as e:
            logger.error(f"插入帖子数据失败: {e}")
            return False
    
    def is_post_exists(self, post_id: str) -> bool:
        """检查帖子是否已存在
        
        Args:
            post_id: 帖子ID
            
        Returns:
            是否存在
        """
        try:
            if not self.connection:
                return False
            
            cursor = self.connection.execute(
                "SELECT 1 FROM posts WHERE post_id = ? LIMIT 1",
                (post_id,)
            )
            return cursor.fetchone() is not None
            
        except Exception as e:
            logger.error(f"检查帖子是否存在失败: {e}")
            return False
    
    def get_posts_by_keyword(self, keyword: str, limit: Optional[int] = None) -> List[Dict]:
        """根据搜索关键词获取帖子
        
        Args:
            keyword: 搜索关键词
            limit: 限制返回数量
            
        Returns:
            帖子列表
        """
        try:
            if not self.connection:
                return []
            
            sql = "SELECT * FROM posts WHERE search_keyword = ? ORDER BY created_at DESC"
            params = [keyword]
            
            if limit:
                sql += " LIMIT ?"
                params.append(limit)
            
            cursor = self.connection.execute(sql, params)
            rows = cursor.fetchall()
            
            # 转换为字典列表，并解析JSON字段
            posts = []
            for row in rows:
                post = dict(row)
                # 解析JSON字段
                try:
                    post['images'] = json.loads(post['images']) if post['images'] else []
                    post['tags'] = json.loads(post['tags']) if post['tags'] else []
                    post['downloaded_images'] = json.loads(post['downloaded_images']) if post['downloaded_images'] else []
                except json.JSONDecodeError:
                    logger.warning(f"解析JSON字段失败，帖子ID: {post['post_id']}")
                    post['images'] = []
                    post['tags'] = []
                    post['downloaded_images'] = []
                
                posts.append(post)
            
            return posts
            
        except Exception as e:
            logger.error(f"根据关键词查询帖子失败: {e}")
            return []
    
    def get_all_posts(self, limit: Optional[int] = None) -> List[Dict]:
        """获取所有帖子
        
        Args:
            limit: 限制返回数量
            
        Returns:
            帖子列表
        """
        try:
            if not self.connection:
                return []
            
            sql = "SELECT * FROM posts ORDER BY created_at DESC"
            params = []
            
            if limit:
                sql += " LIMIT ?"
                params.append(limit)
            
            cursor = self.connection.execute(sql, params)
            rows = cursor.fetchall()
            
            # 转换为字典列表，并解析JSON字段
            posts = []
            for row in rows:
                post = dict(row)
                # 解析JSON字段
                try:
                    post['images'] = json.loads(post['images']) if post['images'] else []
                    post['tags'] = json.loads(post['tags']) if post['tags'] else []
                    post['downloaded_images'] = json.loads(post['downloaded_images']) if post['downloaded_images'] else []
                except json.JSONDecodeError:
                    logger.warning(f"解析JSON字段失败，帖子ID: {post['post_id']}")
                    post['images'] = []
                    post['tags'] = []
                    post['downloaded_images'] = []
                
                posts.append(post)
            
            return posts
            
        except Exception as e:
            logger.error(f"查询所有帖子失败: {e}")
            return []
    
    def get_post_count(self) -> int:
        """获取帖子总数
        
        Returns:
            帖子总数
        """
        try:
            if not self.connection:
                return 0
            
            cursor = self.connection.execute("SELECT COUNT(*) FROM posts")
            return cursor.fetchone()[0]
            
        except Exception as e:
            logger.error(f"获取帖子总数失败: {e}")
            return 0
    
    def get_keywords_stats(self) -> List[Dict]:
        """获取关键词统计信息
        
        Returns:
            关键词统计列表，包含关键词和对应的帖子数量
        """
        try:
            if not self.connection:
                return []
            
            cursor = self.connection.execute("""
                SELECT search_keyword, COUNT(*) as count 
                FROM posts 
                GROUP BY search_keyword 
                ORDER BY count DESC
            """)
            
            return [{"keyword": row[0], "count": row[1]} for row in cursor.fetchall()]
            
        except Exception as e:
            logger.error(f"获取关键词统计失败: {e}")
            return []
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            logger.info("数据库连接已关闭")
    
    def __del__(self):
        """析构函数，确保数据库连接被关闭"""
        self.close()
